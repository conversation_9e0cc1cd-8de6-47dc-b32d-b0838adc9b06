import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { useToast } from '../ui/toast';
import { useAuthStore } from '../../stores/authStore';
import { 
  Share2, 
  Mail, 
  User, 
  Shield, 
  Clock, 
  Trash2, 
  Plus,
  Users,
  Send,
  AlertCircle
} from 'lucide-react';
import type { 
  DashboardShare, 
  PermissionLevel,
  SendInvitationRequest,
  MySharedDashboardsResponse 
} from '../../types/dashboard';

export const DashboardSharing: React.FC = () => {
  const { session, status } = useAuthStore();
  const { showSuccess, showError, ToastContainer } = useToast();
  
  const [sharedByMe, setSharedByMe] = useState<DashboardShare[]>([]);
  const [sharedWithMe, setSharedWithMe] = useState<DashboardShare[]>([]);
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  
  // Form state for sending invitations
  const [inviteEmail, setInviteEmail] = useState('');
  const [invitePermission, setInvitePermission] = useState<PermissionLevel>('VIEW');

  // Fetch shared dashboards
  const fetchSharedDashboards = async () => {
    if (status !== 'authenticated') return;

    try {
      setLoading(true);
      const response = await fetch('/api/sharing/my-shared-dashboards', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const data: MySharedDashboardsResponse = await response.json();
      
      if (data.success && data.data) {
        setSharedByMe(data.data.shared_by_me);
        setSharedWithMe(data.data.shared_with_me);
      } else {
        console.error('Failed to fetch shared dashboards:', data.error);
      }
    } catch (error) {
      console.error('Error fetching shared dashboards:', error);
    } finally {
      setLoading(false);
    }
  };

  // Send invitation
  const sendInvitation = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!inviteEmail.trim()) {
      showError('Error', 'Please enter an email address');
      return;
    }

    try {
      setSending(true);
      
      const requestData: SendInvitationRequest = {
        email: inviteEmail.trim(),
        permission_level: invitePermission
      };

      const response = await fetch('/api/sharing/send-invitation', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      });

      const data = await response.json();
      
      if (data.success) {
        showSuccess('Invitation Sent', data.data?.message || 'Invitation sent successfully');
        setInviteEmail('');
        setInvitePermission('VIEW');
        // Refresh the shared dashboards list
        fetchSharedDashboards();
      } else {
        showError('Error', data.error || 'Failed to send invitation');
      }
    } catch (error) {
      console.error('Error sending invitation:', error);
      showError('Error', 'Failed to send invitation');
    } finally {
      setSending(false);
    }
  };

  // Remove share (revoke access)
  const removeShare = async (shareId: string) => {
    if (!confirm('Are you sure you want to revoke access for this user?')) {
      return;
    }

    try {
      // This would need a new API endpoint to revoke access
      // For now, we'll show a placeholder message
      showError('Not Implemented', 'Share removal functionality will be implemented in the next update');
    } catch (error) {
      console.error('Error removing share:', error);
      showError('Error', 'Failed to remove share');
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get permission level color
  const getPermissionColor = (level: string) => {
    switch (level) {
      case 'VIEW':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'EDIT':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'DELETE':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchSharedDashboards();
  }, [status]);

  if (status !== 'authenticated') {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Authentication Required</h3>
        <p className="text-gray-600">Please sign in to access dashboard sharing features.</p>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-8">
        {/* Page Header */}
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <Share2 className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Dashboard Sharing</h1>
          </div>
          <p className="text-gray-600">
            Share your QR code dashboard with other users and manage access permissions.
          </p>
        </div>

        {/* Send Invitation Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Send className="h-5 w-5" />
              <span>Send Invitation</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={sendInvitation} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    placeholder="Enter email address"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="permission" className="block text-sm font-medium text-gray-700 mb-2">
                    Permission Level
                  </label>
                  <select
                    id="permission"
                    value={invitePermission}
                    onChange={(e) => setInvitePermission(e.target.value as PermissionLevel)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="VIEW">View Only</option>
                    <option value="EDIT">View & Edit</option>
                    <option value="DELETE">Full Access</option>
                  </select>
                </div>
              </div>
              
              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={sending}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {sending ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Sending...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Send Invitation
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Shared By Me */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Shared by Me</span>
                <Badge variant="secondary">{sharedByMe.length}</Badge>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-500 mt-2">Loading...</p>
              </div>
            ) : sharedByMe.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Shared Dashboards</h3>
                <p className="text-gray-600">You haven't shared your dashboard with anyone yet.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {sharedByMe.map((share) => (
                  <div
                    key={share.id}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border"
                  >
                    <div className="flex items-center space-x-4">
                      <User className="h-8 w-8 text-gray-400" />
                      <div>
                        <p className="font-medium text-gray-900">
                          {share.shared_with_name || share.shared_with_email}
                        </p>
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <Shield className="h-3 w-3" />
                          <Badge 
                            variant="outline" 
                            className={getPermissionColor(share.permission_level)}
                          >
                            {share.permission_level}
                          </Badge>
                          <span>•</span>
                          <Clock className="h-3 w-3" />
                          <span>Shared {formatDate(share.created_at)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeShare(share.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Shared With Me */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Mail className="h-5 w-5" />
              <span>Shared with Me</span>
              <Badge variant="secondary">{sharedWithMe.length}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {sharedWithMe.length === 0 ? (
              <div className="text-center py-8">
                <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Shared Dashboards</h3>
                <p className="text-gray-600">No one has shared their dashboard with you yet.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {sharedWithMe.map((share) => (
                  <div
                    key={share.id}
                    className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200"
                  >
                    <div className="flex items-center space-x-4">
                      <User className="h-8 w-8 text-blue-600" />
                      <div>
                        <p className="font-medium text-gray-900">
                          {share.owner_name || share.owner_email}
                        </p>
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <Shield className="h-3 w-3" />
                          <Badge 
                            variant="outline" 
                            className={getPermissionColor(share.permission_level)}
                          >
                            {share.permission_level}
                          </Badge>
                          <span>•</span>
                          <Clock className="h-3 w-3" />
                          <span>Shared {formatDate(share.created_at)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.location.href = '/dashboard'}
                      className="text-blue-600 hover:text-blue-700 hover:bg-blue-100"
                    >
                      View Dashboard
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      
      <ToastContainer />
    </>
  );
};

export default DashboardSharing;
