import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { useAuthStore } from '../../stores/authStore';
import { 
  User, 
  ChevronDown, 
  ChevronUp, 
  Home,
  Share2,
  Crown
} from 'lucide-react';

interface SharedDashboardOwner {
  owner_id: string;
  permission_level: string;
  name: string;
  email: string;
  picture?: string;
}

interface DashboardContextSwitcherProps {
  currentUserId?: string;
  onUserChange: (userId: string, userName: string, isShared: boolean) => void;
  className?: string;
}

export const DashboardContextSwitcher: React.FC<DashboardContextSwitcherProps> = ({
  currentUserId,
  onUserChange,
  className = ''
}) => {
  const { session, status } = useAuthStore();
  const [sharedOwners, setSharedOwners] = useState<SharedDashboardOwner[]>([]);
  const [loading, setLoading] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedUser, setSelectedUser] = useState<{
    id: string;
    name: string;
    isShared: boolean;
  } | null>(null);

  // Fetch shared dashboard owners
  const fetchSharedOwners = async () => {
    if (status !== 'authenticated') return;

    try {
      setLoading(true);
      const response = await fetch('/api/sharing/my-shared-dashboards', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const data = await response.json();
      
      if (data.success && data.data) {
        setSharedOwners(data.data.shared_with_me || []);
      }
    } catch (error) {
      console.error('Error fetching shared owners:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle user selection
  const handleUserSelect = (userId: string, userName: string, isShared: boolean) => {
    setSelectedUser({ id: userId, name: userName, isShared });
    onUserChange(userId, userName, isShared);
    setIsExpanded(false);
  };

  // Initialize with current user or URL parameter
  useEffect(() => {
    if (session?.user && !selectedUser) {
      // Check URL for userId parameter
      const urlParams = new URLSearchParams(window.location.search);
      const urlUserId = urlParams.get('userId');

      if (urlUserId && urlUserId !== (session.user.id || session.user.email)) {
        // URL specifies a different user - check if it's in shared owners
        const sharedOwner = sharedOwners.find(owner => owner.owner_id === urlUserId);
        if (sharedOwner) {
          const userName = sharedOwner.name || sharedOwner.email;
          setSelectedUser({
            id: urlUserId,
            name: userName,
            isShared: true
          });
          onUserChange(urlUserId, userName, true);
          return;
        }
      }

      // Default to current user
      const currentUserName = session.user.name || session.user.email;
      const currentUserId = session.user.id || session.user.email;
      setSelectedUser({
        id: currentUserId,
        name: currentUserName,
        isShared: false
      });
      onUserChange(currentUserId, currentUserName, false);
    }
  }, [session, selectedUser, sharedOwners, onUserChange]);

  // Fetch shared owners on mount
  useEffect(() => {
    fetchSharedOwners();
  }, [status]);

  if (status !== 'authenticated' || !session?.user) {
    return null;
  }

  const currentUser = session.user;
  const hasSharedDashboards = sharedOwners.length > 0;

  return (
    <div className={`mb-6 ${className}`}>
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Share2 className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-lg text-blue-900">
                Dashboard View
              </CardTitle>
              {hasSharedDashboards && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  {sharedOwners.length + 1} available
                </Badge>
              )}
            </div>
            {hasSharedDashboards && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-blue-600 hover:text-blue-800"
              >
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          {/* Current Selection */}
          <div className="mb-4">
            <div className="text-sm text-gray-600 mb-2">Currently viewing:</div>
            <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                {selectedUser?.isShared ? (
                  <Share2 className="h-4 w-4 text-white" />
                ) : (
                  <Crown className="h-4 w-4 text-white" />
                )}
              </div>
              <div className="flex-1">
                <div className="font-medium text-gray-900">
                  {selectedUser?.name || 'Loading...'}
                </div>
                <div className="text-sm text-gray-600">
                  {selectedUser?.isShared ? 'Shared Dashboard' : 'Your Dashboard'}
                </div>
              </div>
              {selectedUser?.isShared && (
                <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                  Shared
                </Badge>
              )}
            </div>
          </div>

          {/* Available Dashboards */}
          {hasSharedDashboards && isExpanded && (
            <div className="space-y-2">
              <div className="text-sm text-gray-600 mb-2">Switch to:</div>
              
              {/* Own Dashboard */}
              <button
                onClick={() => handleUserSelect(
                  currentUser.id || currentUser.email,
                  currentUser.name || currentUser.email,
                  false
                )}
                className={`w-full flex items-center space-x-3 p-3 rounded-lg border transition-colors ${
                  !selectedUser?.isShared
                    ? 'bg-blue-50 border-blue-200'
                    : 'bg-white border-gray-200 hover:bg-gray-50'
                }`}
              >
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <Home className="h-4 w-4 text-white" />
                </div>
                <div className="flex-1 text-left">
                  <div className="font-medium text-gray-900">
                    {currentUser.name || currentUser.email}
                  </div>
                  <div className="text-sm text-gray-600">Your Dashboard</div>
                </div>
                <Crown className="h-4 w-4 text-blue-600" />
              </button>

              {/* Shared Dashboards */}
              {sharedOwners.map((owner) => (
                <button
                  key={owner.owner_id}
                  onClick={() => handleUserSelect(
                    owner.owner_id,
                    owner.name || owner.email,
                    true
                  )}
                  className={`w-full flex items-center space-x-3 p-3 rounded-lg border transition-colors ${
                    selectedUser?.id === owner.owner_id
                      ? 'bg-green-50 border-green-200'
                      : 'bg-white border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                    <User className="h-4 w-4 text-white" />
                  </div>
                  <div className="flex-1 text-left">
                    <div className="font-medium text-gray-900">
                      {owner.name || owner.email}
                    </div>
                    <div className="text-sm text-gray-600">
                      Shared Dashboard • {owner.permission_level} access
                    </div>
                  </div>
                  <Share2 className="h-4 w-4 text-green-600" />
                </button>
              ))}
            </div>
          )}

          {/* No Shared Dashboards Message */}
          {!hasSharedDashboards && !loading && (
            <div className="text-center py-4">
              <div className="text-sm text-gray-500">
                No shared dashboards available. When someone shares their dashboard with you, it will appear here.
              </div>
            </div>
          )}

          {/* Loading State */}
          {loading && (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              <div className="text-sm text-gray-500 mt-2">Loading shared dashboards...</div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default DashboardContextSwitcher;
