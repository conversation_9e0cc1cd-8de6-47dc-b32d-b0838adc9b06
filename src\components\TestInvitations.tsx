import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { useToast } from './ui/toast';
import { useAuthStore } from '../stores/authStore';
import { 
  TestTube, 
  Mail, 
  User, 
  Shield, 
  Clock, 
  Check, 
  X, 
  AlertCircle,
  RefreshCw,
  Send,
  Database
} from 'lucide-react';
import type { Invitation } from '../types/dashboard';

export const TestInvitations: React.FC = () => {
  const { session, status } = useAuthStore();
  const { showSuccess, showError, showInfo, ToastContainer } = useToast();
  
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [loading, setLoading] = useState(false);
  const [responding, setResponding] = useState<string | null>(null);
  const [testEmail, setTestEmail] = useState('');

  // Fetch invitations
  const fetchInvitations = async () => {
    if (status !== 'authenticated' || !session?.user?.email) {
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/sharing/my-invitations', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const data = await response.json();
      
      if (data.success && data.data) {
        setInvitations(data.data);
        showInfo('Data Loaded', `Found ${data.data.length} pending invitations`);
      } else {
        console.error('Failed to fetch invitations:', data.error);
        showError('API Error', data.error || 'Failed to fetch invitations');
      }
    } catch (error) {
      console.error('Error fetching invitations:', error);
      showError('Network Error', 'Failed to connect to API');
    } finally {
      setLoading(false);
    }
  };

  // Send test invitation
  const sendTestInvitation = async () => {
    if (!testEmail.trim()) {
      showError('Validation Error', 'Please enter an email address');
      return;
    }

    try {
      const response = await fetch('/api/sharing/send-invitation', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: testEmail.trim(),
          permission_level: 'VIEW'
        })
      });

      const data = await response.json();
      
      if (data.success) {
        showSuccess('Test Invitation Sent', data.data?.message || 'Invitation sent successfully');
        setTestEmail('');
      } else {
        showError('Send Failed', data.error || 'Failed to send invitation');
      }
    } catch (error) {
      console.error('Error sending invitation:', error);
      showError('Network Error', 'Failed to send invitation');
    }
  };

  // Respond to invitation
  const respondToInvitation = async (invitationId: string, action: 'accept' | 'reject') => {
    try {
      setResponding(invitationId);
      
      const response = await fetch('/api/sharing/respond-invitation', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          invitation_id: invitationId,
          action
        })
      });

      const data = await response.json();
      
      if (data.success) {
        showSuccess(
          `Invitation ${action === 'accept' ? 'Accepted' : 'Rejected'}`,
          data.data?.message
        );
        
        // Remove the invitation from the list
        setInvitations(prev => prev.filter(inv => inv.id !== invitationId));
      } else {
        showError('Response Failed', data.error || 'Failed to respond to invitation');
      }
    } catch (error) {
      console.error('Error responding to invitation:', error);
      showError('Network Error', 'Failed to respond to invitation');
    } finally {
      setResponding(null);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // Get permission level color
  const getPermissionColor = (level: string) => {
    switch (level) {
      case 'VIEW':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'EDIT':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'DELETE':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Fetch invitations on component mount
  useEffect(() => {
    fetchInvitations();
  }, [status, session]);

  if (status !== 'authenticated') {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Authentication Required</h3>
        <p className="text-gray-600">Please sign in to test the invitation system.</p>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-8">
        {/* Page Header */}
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <TestTube className="h-8 w-8 text-purple-600" />
            <h1 className="text-3xl font-bold text-gray-900">Invitation System Test</h1>
          </div>
          <p className="text-gray-600">
            Test the invitation system functionality with detailed debugging information.
          </p>
        </div>

        {/* User Info */}
        <Card className="border-l-4 border-l-purple-500">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Current User</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Email:</strong> {session?.user?.email}</p>
              <p><strong>Name:</strong> {session?.user?.name || 'Not set'}</p>
              <p><strong>ID:</strong> {session?.user?.id || 'Using email as ID'}</p>
            </div>
          </CardContent>
        </Card>

        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Send className="h-5 w-5" />
              <span>Send Test Invitation</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label htmlFor="testEmail" className="block text-sm font-medium text-gray-700 mb-2">
                  Test Email Address
                </label>
                <input
                  type="email"
                  id="testEmail"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  placeholder="Enter email to send test invitation"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
              
              <div className="flex space-x-2">
                <Button
                  onClick={sendTestInvitation}
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  <Send className="h-4 w-4 mr-2" />
                  Send Test Invitation
                </Button>
                
                <Button
                  variant="outline"
                  onClick={fetchInvitations}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh Data
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="h-5 w-5" />
              <span>API Status</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                <div className="text-green-600 font-semibold">Authentication</div>
                <div className="text-sm text-green-700">✓ Connected</div>
              </div>
              
              <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="text-blue-600 font-semibold">Database</div>
                <div className="text-sm text-blue-700">✓ Available</div>
              </div>
              
              <div className="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                <div className="text-purple-600 font-semibold">Invitations</div>
                <div className="text-sm text-purple-700">{invitations.length} pending</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pending Invitations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Mail className="h-5 w-5" />
                <span>Pending Invitations</span>
                <Badge variant="secondary">{invitations.length}</Badge>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchInvitations}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
                <p className="text-gray-500 mt-2">Loading invitations...</p>
              </div>
            ) : invitations.length === 0 ? (
              <div className="text-center py-8">
                <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Pending Invitations</h3>
                <p className="text-gray-600">Send yourself a test invitation to see it appear here.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {invitations.map((invitation) => (
                  <div
                    key={invitation.id}
                    className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm"
                  >
                    <div className="space-y-3">
                      {/* Invitation Details */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <strong>ID:</strong> {invitation.id}
                        </div>
                        <div>
                          <strong>Owner:</strong> {invitation.owner_name || invitation.owner_email}
                        </div>
                        <div>
                          <strong>Email:</strong> {invitation.email}
                        </div>
                        <div>
                          <strong>Permission:</strong>
                          <Badge 
                            variant="outline" 
                            className={`ml-2 ${getPermissionColor(invitation.permission_level)}`}
                          >
                            {invitation.permission_level}
                          </Badge>
                        </div>
                        <div>
                          <strong>Created:</strong> {formatDate(invitation.created_at)}
                        </div>
                        <div>
                          <strong>Expires:</strong> {formatDate(invitation.expires_at)}
                        </div>
                      </div>
                      
                      {/* Action Buttons */}
                      <div className="flex space-x-2 pt-2 border-t">
                        <Button
                          size="sm"
                          onClick={() => respondToInvitation(invitation.id, 'accept')}
                          disabled={responding === invitation.id}
                          className="bg-green-600 hover:bg-green-700 text-white"
                        >
                          <Check className="h-3 w-3 mr-1" />
                          Accept
                        </Button>
                        
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => respondToInvitation(invitation.id, 'reject')}
                          disabled={responding === invitation.id}
                          className="border-red-200 text-red-700 hover:bg-red-50"
                        >
                          <X className="h-3 w-3 mr-1" />
                          Reject
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Testing Instructions */}
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader>
            <CardTitle className="text-blue-900">Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent className="text-sm space-y-2">
            <ol className="list-decimal list-inside space-y-1">
              <li>Use the form above to send a test invitation to any email address</li>
              <li>If you send an invitation to your own email, it will appear in the list below</li>
              <li>Click "Accept" or "Reject" to test the response functionality</li>
              <li>Check the browser console for detailed API logs</li>
              <li>Use the "Refresh Data" button to reload invitations from the database</li>
            </ol>
          </CardContent>
        </Card>
      </div>
      
      <ToastContainer />
    </>
  );
};

export default TestInvitations;
