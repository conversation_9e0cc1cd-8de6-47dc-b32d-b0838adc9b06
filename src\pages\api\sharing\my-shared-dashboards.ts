import type { APIRoute } from 'astro';
import { getDatabase } from '../../../lib/database';
import { getUserIdFromRequest, isAuthenticated } from '../../../lib/auth-utils';
import type { MySharedDashboardsResponse, DashboardShare } from '../../../types/dashboard';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Get database connection
    const db = getDatabase(locals.runtime.env);

    // Check authentication
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication required'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user information
    const userId = getUserIdFromRequest(request);

    if (!userId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'User ID not found'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get dashboards shared BY the current user
    const sharedByMeResult = await db.prepare(`
      SELECT 
        ds.id,
        ds.owner_id,
        ds.shared_with_id,
        ds.permission_level,
        ds.status,
        ds.created_at,
        ds.updated_at,
        u.name as shared_with_name,
        u.email as shared_with_email
      FROM dashboard_shares ds
      LEFT JOIN users u ON ds.shared_with_id = u.id
      WHERE ds.owner_id = ? AND ds.status = 'active'
      ORDER BY ds.created_at DESC
    `).bind(userId).all();

    // Get dashboards shared WITH the current user
    const sharedWithMeResult = await db.prepare(`
      SELECT 
        ds.id,
        ds.owner_id,
        ds.shared_with_id,
        ds.permission_level,
        ds.status,
        ds.created_at,
        ds.updated_at,
        u.name as owner_name,
        u.email as owner_email
      FROM dashboard_shares ds
      LEFT JOIN users u ON ds.owner_id = u.id
      WHERE ds.shared_with_id = ? AND ds.status = 'active'
      ORDER BY ds.created_at DESC
    `).bind(userId).all();

    // Transform database results to API format
    const sharedByMe: DashboardShare[] = sharedByMeResult.results.map((row: any) => ({
      id: row.id,
      owner_id: row.owner_id,
      shared_with_id: row.shared_with_id,
      shared_with_name: row.shared_with_name,
      shared_with_email: row.shared_with_email,
      permission_level: row.permission_level,
      status: row.status,
      created_at: row.created_at,
      updated_at: row.updated_at
    }));

    const sharedWithMe: DashboardShare[] = sharedWithMeResult.results.map((row: any) => ({
      id: row.id,
      owner_id: row.owner_id,
      owner_name: row.owner_name,
      owner_email: row.owner_email,
      shared_with_id: row.shared_with_id,
      permission_level: row.permission_level,
      status: row.status,
      created_at: row.created_at,
      updated_at: row.updated_at
    }));

    console.log(`Found ${sharedByMe.length} dashboards shared by user, ${sharedWithMe.length} shared with user`);

    const response: MySharedDashboardsResponse = {
      success: true,
      data: {
        shared_by_me: sharedByMe,
        shared_with_me: sharedWithMe
      }
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('Error fetching shared dashboards:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
