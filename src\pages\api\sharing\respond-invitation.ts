import type { APIRoute } from 'astro';
import { getDatabase } from '../../../lib/database';
import { getUserIdFromRequest, getUserFromRequest, isAuthenticated } from '../../../lib/auth-utils';
import { v4 as uuidv4 } from 'uuid';
import type { RespondInvitationRequest, RespondInvitationResponse } from '../../../types/dashboard';

export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Get database connection
    const db = getDatabase(locals.runtime.env);

    // Check authentication
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication required'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user information
    const userId = getUserIdFromRequest(request);
    const user = getUserFromRequest(request);

    if (!userId || !user || !user.email) {
      return new Response(JSON.stringify({
        success: false,
        error: 'User information not found'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse request body
    const body = await request.json() as RespondInvitationRequest;
    const { invitation_id, action } = body;

    // Validate input
    if (!invitation_id || !action) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invitation ID and action are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate action
    if (!['accept', 'reject'].includes(action)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid action. Must be "accept" or "reject"'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get the invitation and verify it belongs to the current user
    const invitation = await db.prepare(`
      SELECT 
        id, owner_id, email, permission_level, status, expires_at, created_at
      FROM share_invitations 
      WHERE id = ? AND email = ? AND status = 'pending'
    `).bind(invitation_id, user.email.toLowerCase()).first();

    if (!invitation) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invitation not found or not accessible'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if invitation has expired
    const now = new Date();
    const expiresAt = new Date(invitation.expires_at);
    if (now > expiresAt) {
      // Mark as expired
      await db.prepare(`
        UPDATE share_invitations 
        SET status = 'expired' 
        WHERE id = ?
      `).bind(invitation_id).run();

      return new Response(JSON.stringify({
        success: false,
        error: 'Invitation has expired'
      }), {
        status: 410,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    let response: RespondInvitationResponse;

    if (action === 'accept') {
      // Check if user already has access to this dashboard
      const existingShare = await db.prepare(`
        SELECT id FROM dashboard_shares 
        WHERE owner_id = ? AND shared_with_id = ? AND status = 'active'
      `).bind(invitation.owner_id, userId).first();

      if (existingShare) {
        // Mark invitation as accepted but don't create duplicate share
        await db.prepare(`
          UPDATE share_invitations 
          SET status = 'accepted' 
          WHERE id = ?
        `).bind(invitation_id).run();

        response = {
          success: true,
          data: {
            message: 'You already have access to this dashboard',
            share_id: existingShare.id
          }
        };
      } else {
        // Create new dashboard share
        const shareId = uuidv4();
        
        await db.prepare(`
          INSERT INTO dashboard_shares (
            id, owner_id, shared_with_id, permission_level, status, created_at, updated_at
          ) VALUES (?, ?, ?, ?, 'active', datetime('now'), datetime('now'))
        `).bind(
          shareId,
          invitation.owner_id,
          userId,
          invitation.permission_level
        ).run();

        // Mark invitation as accepted
        await db.prepare(`
          UPDATE share_invitations 
          SET status = 'accepted' 
          WHERE id = ?
        `).bind(invitation_id).run();

        console.log('Dashboard share created:', {
          share_id: shareId,
          owner_id: invitation.owner_id,
          shared_with_id: userId,
          permission_level: invitation.permission_level
        });

        response = {
          success: true,
          data: {
            message: 'Invitation accepted successfully',
            share_id: shareId
          }
        };
      }
    } else {
      // Reject invitation
      await db.prepare(`
        UPDATE share_invitations 
        SET status = 'expired' 
        WHERE id = ?
      `).bind(invitation_id).run();

      console.log('Invitation rejected:', {
        invitation_id,
        user_email: user.email
      });

      response = {
        success: true,
        data: {
          message: 'Invitation rejected'
        }
      };
    }

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error responding to invitation:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
