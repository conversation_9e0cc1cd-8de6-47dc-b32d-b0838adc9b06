import type { APIRoute } from 'astro';
import { getDatabase } from '../../../lib/database';
import { getUserIdFromRequest, getUserFromRequest, isAuthenticated } from '../../../lib/auth-utils';
import { v4 as uuidv4 } from 'uuid';
import type { SendInvitationRequest, SendInvitationResponse } from '../../../types/dashboard';

export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Get database connection
    const db = getDatabase(locals.runtime.env);

    // Check authentication
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication required'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user information
    const userId = getUserIdFromRequest(request);
    const user = getUserFromRequest(request);

    if (!userId || !user) {
      return new Response(JSON.stringify({
        success: false,
        error: 'User information not found'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse request body
    const body = await request.json() as SendInvitationRequest;
    const { email, permission_level } = body;

    // Validate input
    if (!email || !permission_level) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Email and permission level are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate permission level
    if (!['VIEW', 'EDIT', 'DELETE'].includes(permission_level)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid permission level. Must be VIEW, EDIT, or DELETE'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid email format'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if user is trying to invite themselves
    if (email.toLowerCase() === user.email.toLowerCase()) {
      return new Response(JSON.stringify({
        success: false,
        error: 'You cannot invite yourself'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if there's already a pending invitation for this email
    const existingInvitation = await db.prepare(`
      SELECT id, status FROM share_invitations 
      WHERE owner_id = ? AND email = ? AND status = 'pending' AND expires_at > datetime('now')
    `).bind(userId, email.toLowerCase()).first();

    if (existingInvitation) {
      return new Response(JSON.stringify({
        success: false,
        error: 'A pending invitation already exists for this email address'
      }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if there's already an active share with this user
    const existingShare = await db.prepare(`
      SELECT ds.id FROM dashboard_shares ds
      JOIN users u ON ds.shared_with_id = u.id
      WHERE ds.owner_id = ? AND u.email = ? AND ds.status = 'active'
    `).bind(userId, email.toLowerCase()).first();

    if (existingShare) {
      return new Response(JSON.stringify({
        success: false,
        error: 'This user already has access to your dashboard'
      }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Generate invitation ID and token
    const invitationId = uuidv4();
    const token = uuidv4();
    
    // Set expiration date (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Create invitation
    await db.prepare(`
      INSERT INTO share_invitations (
        id, owner_id, email, permission_level, token, status, expires_at, created_at
      ) VALUES (?, ?, ?, ?, ?, 'pending', ?, datetime('now'))
    `).bind(
      invitationId,
      userId,
      email.toLowerCase(),
      permission_level,
      token,
      expiresAt.toISOString()
    ).run();

    console.log('Invitation created:', {
      id: invitationId,
      owner_id: userId,
      email: email.toLowerCase(),
      permission_level
    });

    const response: SendInvitationResponse = {
      success: true,
      data: {
        invitation_id: invitationId,
        message: `Invitation sent to ${email} with ${permission_level} permissions`
      }
    };

    return new Response(JSON.stringify(response), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error sending invitation:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
