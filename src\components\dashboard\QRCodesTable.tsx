import React, { useState, useMemo } from "react";
import { format } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { But<PERSON> } from "../ui/button";
import { Badge } from "../ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Input } from "../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

import {
  Eye,
  Download,
  MoreHorizontal,
  Search,
  Filter,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Trash2,
  Edit,
  Copy,
  ExternalLink,
  Calendar,
  BarChart3,
  QrCode,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "../ui/dropdown-menu";
import { FormatSelectionDialog } from "./FormatSelectionDialog";
import type { QRCode } from "../../types/dashboard";

type SortField = "name" | "created_at" | "scanCount" | "content_type";
type SortDirection = "asc" | "desc";

interface SortConfig {
  field: SortField;
  direction: SortDirection;
}

interface QRCodesTableProps {
  qrCodes: QRCode[];
  onViewDetails: (qrCodeId: string) => void;
  onDownloadQR: (qrCodeId: string, format?: "png" | "jpeg" | "svg") => void;
  isSharedDashboard?: boolean;
  permission?: 'VIEW' | 'EDIT' | 'DELETE';
}

// Helper function to get the display URL for a QR code
const getDisplayUrl = (qrCode: QRCode): string => {
  // For dynamic QR codes, show tracking URL; for static QR codes, show original URL
  if (qrCode.dynamic === 1 && qrCode.custom_slug && qrCode.tracking_domain) {
    // Dynamic QR code - show tracking URL
    return `https://${qrCode.tracking_domain}/${qrCode.custom_slug}`;
  } else {
    // Static QR code - show original URL or data
    return qrCode.content_type === "url"
      ? qrCode.original_url || JSON.parse(qrCode.data).data
      : JSON.parse(qrCode.data).data;
  }
};

// Enhanced table header component with sorting
const SortableHeader: React.FC<{
  children: React.ReactNode;
  field: SortField;
  sortConfig: SortConfig | null;
  onSort: (field: SortField) => void;
  className?: string;
}> = ({ children, field, sortConfig, onSort, className = "" }) => {
  const isSorted = sortConfig?.field === field;
  const direction = isSorted ? sortConfig.direction : null;

  return (
    <TableHead
      className={`cursor-pointer hover:bg-gray-50 transition-colors select-none ${className}`}
      onClick={() => onSort(field)}
    >
      <div className="flex items-center space-x-1">
        <span>{children}</span>
        <div className="flex flex-col">
          {direction === "asc" ? (
            <ArrowUp className="h-3 w-3 text-blue-600" />
          ) : direction === "desc" ? (
            <ArrowDown className="h-3 w-3 text-blue-600" />
          ) : (
            <ArrowUpDown className="h-3 w-3 text-gray-400" />
          )}
        </div>
      </div>
    </TableHead>
  );
};

const StatusBadge: React.FC<{ qrCode: QRCode }> = ({ qrCode }) => {
  const isActive = qrCode.dynamic === 1;
  return (
    <div className="flex items-center space-x-2">
      <div
        className={`w-2 h-2 rounded-full ${
          isActive ? "bg-green-500 animate-pulse" : "bg-gray-400"
        }`}
      />
      <Badge
        variant={isActive ? "default" : "secondary"}
        className={`${
          isActive
            ? "bg-green-50 text-green-700 border-green-200 hover:bg-green-100"
            : "bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100"
        } transition-colors font-medium`}
      >
        {isActive ? "Active" : "Inactive"}
      </Badge>
    </div>
  );
};

const ActionButtons: React.FC<{
  qrCodeId: string;
  qrCode: QRCode;
  onViewDetails: (id: string) => void;
  onDownloadQR: (id: string, format?: "png" | "jpeg" | "svg") => void;
}> = ({ qrCodeId, qrCode, onViewDetails, onDownloadQR }) => {
  const [showCopySuccess, setShowCopySuccess] = useState(false);
  const [showFormatDialog, setShowFormatDialog] = useState(false);

  const handleCopyUrl = async () => {
    try {
      const url = getDisplayUrl(qrCode);

      await navigator.clipboard.writeText(url);
      setShowCopySuccess(true);
      setTimeout(() => setShowCopySuccess(false), 2000);
      console.log("URL copied to clipboard:", url);
    } catch (error) {
      console.error("Failed to copy URL:", error);
      // Fallback for older browsers
      try {
        const textArea = document.createElement("textarea");
        textArea.value = getDisplayUrl(qrCode);
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
        setShowCopySuccess(true);
        setTimeout(() => setShowCopySuccess(false), 2000);
      } catch (fallbackError) {
        console.error("Fallback copy failed:", fallbackError);
        alert("Failed to copy to clipboard");
      }
    }
  };

  const handleEditQR = () => {
    // Navigate to edit page with QR code data
    const editUrl = `/tool/qr-code-generator?edit=${qrCodeId}`;
    window.location.href = editUrl;
  };

  const handleOpenUrl = () => {
    if (qrCode.content_type === "url") {
      const url = getDisplayUrl(qrCode);
      window.open(url, "_blank", "noopener,noreferrer");
    } else {
      alert("This QR code does not contain a URL");
    }
  };

  const handleDeleteQR = async () => {
    if (
      !confirm(
        `Are you sure you want to delete "${
          qrCode.name || "this QR code"
        }"? This action cannot be undone.`
      )
    ) {
      return;
    }

    try {
      const response = await fetch(`/api/qr-codes/${qrCodeId}`, {
        method: "DELETE",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        alert("QR code deleted successfully");
        // Refresh the page to update the list
        window.location.reload();
      } else {
        const error = await response.text();
        console.error("Delete failed:", error);
        alert("Failed to delete QR code");
      }
    } catch (error) {
      console.error("Delete error:", error);
      alert("Error deleting QR code");
    }
  };

  const handleDownloadClick = () => {
    setShowFormatDialog(true);
  };

  const handleFormatSelected = (format: "png" | "jpeg" | "svg") => {
    onDownloadQR(qrCodeId, format);
    setShowFormatDialog(false);
  };

  return (
    <>
      <div className="flex items-center justify-end space-x-1">
        {/* Quick action buttons */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onViewDetails(qrCodeId)}
          className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600 transition-colors"
          title="View Analytics"
        >
          <BarChart3 className="h-4 w-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleDownloadClick}
          className="h-8 w-8 p-0 hover:bg-green-50 hover:text-green-600 transition-colors"
          title="Download QR Code"
        >
          <Download className="h-4 w-4" />
        </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={handleCopyUrl}
        className={`h-8 w-8 p-0 transition-colors relative ${
          showCopySuccess
            ? "bg-green-50 text-green-600"
            : "hover:bg-purple-50 hover:text-purple-600"
        }`}
        title={showCopySuccess ? "Copied!" : "Copy URL"}
      >
        {showCopySuccess ? (
          <svg
            className="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        ) : (
          <Copy className="h-4 w-4" />
        )}
      </Button>

      {/* More actions dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-gray-100 transition-colors"
          >
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem
            onClick={() => onViewDetails(qrCodeId)}
            className="cursor-pointer"
          >
            <Eye className="h-4 w-4 mr-2" />
            View Analytics
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={handleDownloadClick}
            className="cursor-pointer"
          >
            <Download className="h-4 w-4 mr-2" />
            Download QR Code
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleCopyUrl} className="cursor-pointer">
            <Copy className="h-4 w-4 mr-2" />
            {showCopySuccess ? "Copied!" : "Copy URL"}
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleOpenUrl} className="cursor-pointer">
            <ExternalLink className="h-4 w-4 mr-2" />
            Open URL
          </DropdownMenuItem>

          {/* Only show edit/delete actions if not shared dashboard or has appropriate permissions */}
          {!isSharedDashboard && (
            <>
              <DropdownMenuSeparator />
              {/* <DropdownMenuItem onClick={handleEditQR} className="cursor-pointer">
                <Edit className="h-4 w-4 mr-2" />
                Edit QR Code
              </DropdownMenuItem> */}
              <DropdownMenuItem
                onClick={handleDeleteQR}
                className="text-red-600 cursor-pointer hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete QR Code
              </DropdownMenuItem>
            </>
          )}

          {isSharedDashboard && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem disabled className="text-gray-400">
                <Edit className="h-4 w-4 mr-2" />
                Edit (Not Available)
              </DropdownMenuItem>
              <DropdownMenuItem disabled className="text-gray-400">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete (Not Available)
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      </div>

      <FormatSelectionDialog
        isOpen={showFormatDialog}
        onClose={() => setShowFormatDialog(false)}
        onDownload={handleFormatSelected}
        qrCodeName={qrCode.name || "QR Code"}
      />
    </>
  );
};

// Mobile card component for better mobile experience
const MobileQRCard: React.FC<{
  qrCode: QRCode;
  onViewDetails: (id: string) => void;
  onDownloadQR: (id: string, format?: "png" | "jpeg" | "svg") => void;
  isSharedDashboard?: boolean;
}> = ({ qrCode, onViewDetails, onDownloadQR, isSharedDashboard = false }) => (
  <Card className="p-4">
    <div className="flex justify-between items-start mb-3">
      <div className="flex-1">
        <h3 className="font-medium text-sm">
          {qrCode.name || "Unnamed QR Code"}
        </h3>
        <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
          {getDisplayUrl(qrCode)}
        </p>
      </div>
      <StatusBadge qrCode={qrCode} />
    </div>

    <div className="grid grid-cols-2 gap-4 text-xs mb-3">
      <div>
        <span className="text-muted-foreground">Created:</span>
        <div className="font-medium">
          {format(new Date(qrCode.created_at), "MMM dd, yyyy")}
        </div>
      </div>
      <div>
        <span className="text-muted-foreground">Scans:</span>
        <div className="font-medium">{qrCode.scanCount.toLocaleString()}</div>
      </div>
    </div>

    <div className="flex flex-wrap gap-1 mb-3">
      <Badge variant="outline" className="text-xs">
        {qrCode.content_type}
      </Badge>
      {qrCode.dynamic === 1 && (
        <Badge variant="outline" className="text-xs">
          dynamic
        </Badge>
      )}
    </div>

    <ActionButtons
      qrCodeId={qrCode.id}
      qrCode={qrCode}
      onViewDetails={onViewDetails}
      onDownloadQR={onDownloadQR}
    />
  </Card>
);

export const QRCodesTable: React.FC<QRCodesTableProps> = ({
  qrCodes,
  onViewDetails,
  onDownloadQR,
  isSharedDashboard = false,
  permission = 'VIEW',
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<
    "all" | "active" | "inactive"
  >("all");
  const [typeFilter, setTypeFilter] = useState<
    "all" | "url" | "text" | "email" | "phone"
  >("all");
  const [sortConfig, setSortConfig] = useState<SortConfig | null>({
    field: "created_at",
    direction: "desc",
  });


  // Filter and sort QR codes
  const filteredAndSortedQRs = useMemo(() => {
    let filtered = qrCodes.filter((qr) => {
      const matchesSearch =
        !searchTerm ||
        (qr.name && qr.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        qr.data.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (qr.original_url &&
          qr.original_url.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesStatus =
        statusFilter === "all" ||
        (statusFilter === "active" && qr.dynamic === 1) ||
        (statusFilter === "inactive" && qr.dynamic === 0);

      const matchesType =
        typeFilter === "all" || qr.content_type === typeFilter;

      return matchesSearch && matchesStatus && matchesType;
    });

    console.log(filtered, "filtered");

    if (sortConfig) {
      filtered.sort((a, b) => {
        let aValue: any = a[sortConfig.field];
        let bValue: any = b[sortConfig.field];

        if (sortConfig.field === "created_at") {
          aValue = new Date(aValue).getTime();
          bValue = new Date(bValue).getTime();
        } else if (sortConfig.field === "name") {
          aValue = aValue || "Unnamed QR Code";
          bValue = bValue || "Unnamed QR Code";
        }

        if (aValue < bValue) return sortConfig.direction === "asc" ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === "asc" ? 1 : -1;
        return 0;
      });
    }

    return filtered;
  }, [qrCodes, searchTerm, statusFilter, typeFilter, sortConfig]);

  const handleSort = (field: SortField) => {
    setSortConfig((current) => {
      if (current?.field === field) {
        return {
          field,
          direction: current.direction === "asc" ? "desc" : "asc",
        };
      }
      return { field, direction: "asc" };
    });
  };






  // Quick action handlers
  const handleCreateNewQR = () => {
    window.location.href = '/tool/qr-code-generator';
  };
  if (qrCodes.length === 0) {
    return (
      <Card className="border-0 shadow-lg">
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-2xl font-bold text-gray-900">
            QR Codes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
              <QrCode className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No QR codes yet
            </h3>
            <p className="text-gray-500 mb-6 max-w-sm mx-auto">
              Get started by creating your first QR code. It's quick and easy!
            </p>
            <Button onClick={handleCreateNewQR} className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200 transform hover:scale-105">
              Create Your First QR Code
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader className="border-b border-gray-100 bg-gray-50/50">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <CardTitle className="text-xl font-bold text-gray-900 flex items-center space-x-2">
            <QrCode className="h-5 w-5 text-blue-600" />
            <span>QR Codes ({filteredAndSortedQRs.length})</span>
          </CardTitle>


        </div>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-3 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search QR codes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500"
            />
          </div>

          {/* <div className="flex space-x-2">
          <Select
              value={statusFilter}
              onValueChange={(value: any) => setStatusFilter(value)}
            >
              <SelectTrigger className="w-32 bg-white border-gray-200">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>  

            <Select
              value={typeFilter}
              onValueChange={(value: any) => setTypeFilter(value)}
            >
              <SelectTrigger className="w-32 bg-white border-gray-200">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="url">URL</SelectItem>
                <SelectItem value="text">Text</SelectItem>
                <SelectItem value="email">Email</SelectItem>
                <SelectItem value="phone">Phone</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" size="sm" className="px-3">
              <Filter className="h-4 w-4" />
            </Button>
          </div> */}
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {/* Desktop Table View */}
        <div className="hidden md:block">
          <Table>
            <TableHeader className="bg-gray-50/50">
              <TableRow className="border-b border-gray-200">
                <SortableHeader
                  field="name"
                  sortConfig={sortConfig}
                  onSort={handleSort}
                >
                  Name
                </SortableHeader>
                <SortableHeader
                  field="created_at"
                  sortConfig={sortConfig}
                  onSort={handleSort}
                >
                  Created
                </SortableHeader>
                <SortableHeader
                  field="scanCount"
                  sortConfig={sortConfig}
                  onSort={handleSort}
                  className="text-right"
                >
                  Scan Count
                </SortableHeader>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedQRs.map((qrCode) => (
                <TableRow
                  key={qrCode.id}
                  className="hover:bg-gray-50/50 transition-colors"
                >
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div
                        className={`w-10 h-10 rounded-lg flex items-center justify-center text-white text-sm font-semibold ${
                          qrCode.content_type === "url"
                            ? "bg-blue-500"
                            : qrCode.content_type === "text"
                            ? "bg-green-500"
                            : qrCode.content_type === "email"
                            ? "bg-purple-500"
                            : qrCode.content_type === "phone"
                            ? "bg-orange-500"
                            : "bg-gray-500"
                        }`}
                      >
                        {qrCode.content_type === "url"
                          ? "🔗"
                          : qrCode.content_type === "text"
                          ? "📝"
                          : qrCode.content_type === "email"
                          ? "📧"
                          : qrCode.content_type === "phone"
                          ? "📞"
                          : "📄"}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-semibold text-gray-900 truncate">
                          {qrCode.name || "Unnamed QR Code"}
                        </div>
                        <div className="text-sm text-gray-500 truncate max-w-xs">
                          {getDisplayUrl(qrCode)}
                        </div>
                        <div className="flex flex-wrap gap-1 mt-1">
                          <Badge
                            variant="outline"
                            className="text-xs bg-gray-50 text-gray-600 border-gray-200"
                          >
                            {qrCode.content_type.toUpperCase()}
                          </Badge>
                          {/* {qrCode.dynamic === 1 && (
                            <Badge variant="outline" className="text-xs bg-blue-50 text-blue-600 border-blue-200">
                              dynamic
                            </Badge>
                          )} */}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {format(new Date(qrCode.created_at), "MMM dd, yyyy")}
                        </div>
                        <div className="text-xs text-gray-500">
                          {format(new Date(qrCode.created_at), "HH:mm")}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end space-x-2">
                      <BarChart3 className="h-4 w-4 text-gray-400" />
                      <div>
                        <div className="font-bold text-lg text-gray-900">
                          {qrCode.scanCount.toLocaleString()}
                        </div>
                        {qrCode.lastScanned && (
                          <div className="text-xs text-gray-500">
                            Last:{" "}
                            {format(new Date(qrCode.lastScanned), "MMM dd")}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <StatusBadge qrCode={qrCode} />
                  </TableCell>
                  <TableCell className="text-right">
                    <ActionButtons
                      qrCodeId={qrCode.id}
                      qrCode={qrCode}
                      onViewDetails={onViewDetails}
                      onDownloadQR={onDownloadQR}
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Mobile Card View */}
        <div className="md:hidden space-y-3 p-4">
          {filteredAndSortedQRs.map((qrCode) => (
            <MobileQRCard
              key={qrCode.id}
              qrCode={qrCode}
              onViewDetails={onViewDetails}
              onDownloadQR={onDownloadQR}
              isSharedDashboard={isSharedDashboard}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default QRCodesTable;
