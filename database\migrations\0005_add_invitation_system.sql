-- Add invitation system tables for dashboard sharing
-- Migration: 0005_add_invitation_system.sql

-- Create share_invitations table
CREATE TABLE IF NOT EXISTS share_invitations (
  id TEXT PRIMARY KEY,
  owner_id TEXT NOT NULL,
  email TEXT NOT NULL,
  permission_level TEXT NOT NULL CHECK (permission_level IN ('VIEW', 'EDIT', 'DELETE')),
  token TEXT UNIQUE NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired')),
  expires_at DATETIME NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create dashboard_shares table
CREATE TABLE IF NOT EXISTS dashboard_shares (
  id TEXT PRIMARY KEY,
  owner_id TEXT NOT NULL,
  shared_with_id TEXT NOT NULL,
  permission_level TEXT NOT NULL CHECK (permission_level IN ('VIEW', 'EDIT', 'DELETE')),
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (shared_with_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE(owner_id, shared_with_id)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_share_invitations_email ON share_invitations(email);
CREATE INDEX IF NOT EXISTS idx_share_invitations_owner_id ON share_invitations(owner_id);
CREATE INDEX IF NOT EXISTS idx_share_invitations_status ON share_invitations(status);
CREATE INDEX IF NOT EXISTS idx_share_invitations_expires_at ON share_invitations(expires_at);
CREATE INDEX IF NOT EXISTS idx_share_invitations_token ON share_invitations(token);

CREATE INDEX IF NOT EXISTS idx_dashboard_shares_owner_id ON dashboard_shares(owner_id);
CREATE INDEX IF NOT EXISTS idx_dashboard_shares_shared_with_id ON dashboard_shares(shared_with_id);
CREATE INDEX IF NOT EXISTS idx_dashboard_shares_status ON dashboard_shares(status);

-- Create triggers to handle updated_at timestamp for dashboard_shares
CREATE TRIGGER IF NOT EXISTS update_dashboard_shares_updated_at 
    AFTER UPDATE ON dashboard_shares
    FOR EACH ROW
    BEGIN
        UPDATE dashboard_shares SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- Create trigger to set updated_at on insert if not provided
CREATE TRIGGER IF NOT EXISTS insert_dashboard_shares_updated_at
    AFTER INSERT ON dashboard_shares
    FOR EACH ROW
    WHEN NEW.updated_at IS NULL
    BEGIN
        UPDATE dashboard_shares SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
