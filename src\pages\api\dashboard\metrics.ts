import type { APIRoute } from 'astro';
import { getDashboardMetrics, getDatabase } from '../../../lib/database';
import { getUserIdFromRequest, isAuthenticated } from '../../../lib/auth-utils';
import { getEffectiveDashboardUserId } from '../../../lib/sharing-utils';
import type { DashboardMetricsResponse } from '../../../types/dashboard';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Get database connection
    const db = getDatabase(locals.runtime.env);

    // Get user ID from session (if available)
    const currentUserId = getUserIdFromRequest(request);
    console.log('Debug: Dashboard metrics - Current User ID:', currentUserId);

    // Check authentication
    if (!isAuthenticated(request) || !currentUserId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication required'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get timezone and requested user from query parameters
    const url = new URL(request.url);
    const userTimezone = url.searchParams.get('timezone') || 'UTC';
    const requestedUserId = url.searchParams.get('userId');
    console.log('Debug: User timezone:', userTimezone);
    console.log('Debug: Requested user ID:', requestedUserId);

    // Get effective user ID (handles shared dashboard access)
    const { userId: effectiveUserId, permission, isShared } = await getEffectiveDashboardUserId(
      db,
      currentUserId,
      requestedUserId || undefined
    );

    console.log('Debug: Effective user ID:', effectiveUserId, 'Permission:', permission, 'Is shared:', isShared);

    // Fetch dashboard metrics for the effective user
    const metrics = await getDashboardMetrics(db, effectiveUserId, userTimezone);
    
    const response: DashboardMetricsResponse = {
      success: true,
      data: {
        ...metrics,
        // Add metadata about the dashboard context
        _context: {
          isShared,
          permission,
          effectiveUserId
        }
      }
    };
    
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  } catch (error) {
    console.error('Dashboard metrics API error:', error);
    
    const response: DashboardMetricsResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch dashboard metrics'
    };
    
    return new Response(JSON.stringify(response), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
