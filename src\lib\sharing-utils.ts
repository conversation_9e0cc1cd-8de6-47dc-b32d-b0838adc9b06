import type { PermissionLevel } from '../types/dashboard';

/**
 * Check if a user has permission to access another user's dashboard
 */
export async function checkDashboardAccess(
  db: D1Database, 
  viewerUserId: string, 
  ownerUserId: string
): Promise<{ hasAccess: boolean; permission?: PermissionLevel }> {
  try {
    // If viewing own dashboard, always allow
    if (viewerUserId === ownerUserId) {
      return { hasAccess: true, permission: 'DELETE' }; // Full access to own dashboard
    }

    // Check if there's an active share relationship
    const shareResult = await db.prepare(`
      SELECT permission_level 
      FROM dashboard_shares 
      WHERE owner_id = ? AND shared_with_id = ? AND status = 'active'
    `).bind(ownerUserId, viewerUserId).first();

    if (shareResult) {
      return { 
        hasAccess: true, 
        permission: shareResult.permission_level as PermissionLevel 
      };
    }

    return { hasAccess: false };
  } catch (error) {
    console.error('Error checking dashboard access:', error);
    return { hasAccess: false };
  }
}

/**
 * Get the effective user ID for dashboard queries
 * This allows viewing shared dashboards by returning the owner's ID when appropriate
 */
export async function getEffectiveDashboardUserId(
  db: D1Database,
  currentUserId: string,
  requestedUserId?: string
): Promise<{ userId: string; permission: PermissionLevel; isShared: boolean }> {
  // If no specific user requested, use current user
  if (!requestedUserId || requestedUserId === currentUserId) {
    return { 
      userId: currentUserId, 
      permission: 'DELETE', // Full access to own dashboard
      isShared: false 
    };
  }

  // Check if current user has access to requested user's dashboard
  const accessCheck = await checkDashboardAccess(db, currentUserId, requestedUserId);
  
  if (accessCheck.hasAccess && accessCheck.permission) {
    return { 
      userId: requestedUserId, 
      permission: accessCheck.permission,
      isShared: true 
    };
  }

  // No access, return current user's dashboard
  return { 
    userId: currentUserId, 
    permission: 'DELETE',
    isShared: false 
  };
}

/**
 * Check if a user has a specific permission level or higher
 */
export function hasPermission(userPermission: PermissionLevel, requiredPermission: PermissionLevel): boolean {
  const permissionLevels = {
    'VIEW': 1,
    'EDIT': 2,
    'DELETE': 3
  };

  return permissionLevels[userPermission] >= permissionLevels[requiredPermission];
}

/**
 * Get user information by ID
 */
export async function getUserById(db: D1Database, userId: string) {
  try {
    const user = await db.prepare(`
      SELECT id, email, name, picture, created_at
      FROM users 
      WHERE id = ?
    `).bind(userId).first();

    return user;
  } catch (error) {
    console.error('Error fetching user by ID:', error);
    return null;
  }
}

/**
 * Get all users that have shared their dashboard with the current user
 */
export async function getSharedDashboardOwners(db: D1Database, currentUserId: string) {
  try {
    const result = await db.prepare(`
      SELECT 
        ds.owner_id,
        ds.permission_level,
        u.name,
        u.email,
        u.picture
      FROM dashboard_shares ds
      LEFT JOIN users u ON ds.owner_id = u.id
      WHERE ds.shared_with_id = ? AND ds.status = 'active'
      ORDER BY u.name, u.email
    `).bind(currentUserId).all();

    return result.results || [];
  } catch (error) {
    console.error('Error fetching shared dashboard owners:', error);
    return [];
  }
}
