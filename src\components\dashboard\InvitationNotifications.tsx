import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { useToast } from '../ui/toast';
import { useAuthStore } from '../../stores/authStore';
import { 
  Mail, 
  Clock, 
  User, 
  Shield, 
  Check, 
  X, 
  AlertCircle,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import type { Invitation } from '../../types/dashboard';

interface InvitationNotificationsProps {
  className?: string;
}

export const InvitationNotifications: React.FC<InvitationNotificationsProps> = ({ 
  className = '' 
}) => {
  const { session, status } = useAuthStore();
  const { showSuccess, showError, ToastContainer } = useToast();
  
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [loading, setLoading] = useState(false);
  const [responding, setResponding] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(true);

  // Fetch invitations
  const fetchInvitations = async () => {
    if (status !== 'authenticated' || !session?.user?.email) {
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/sharing/my-invitations', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const data = await response.json();
      
      if (data.success && data.data) {
        setInvitations(data.data);
      } else {
        console.error('Failed to fetch invitations:', data.error);
      }
    } catch (error) {
      console.error('Error fetching invitations:', error);
    } finally {
      setLoading(false);
    }
  };

  // Respond to invitation
  const respondToInvitation = async (invitationId: string, action: 'accept' | 'reject') => {
    try {
      setResponding(invitationId);
      
      const response = await fetch('/api/sharing/respond-invitation', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          invitation_id: invitationId,
          action
        })
      });

      const data = await response.json();
      
      if (data.success) {
        showSuccess(
          action === 'accept' ? 'Invitation Accepted' : 'Invitation Rejected',
          data.data?.message
        );
        
        // Remove the invitation from the list
        setInvitations(prev => prev.filter(inv => inv.id !== invitationId));
      } else {
        showError('Error', data.error || 'Failed to respond to invitation');
      }
    } catch (error) {
      console.error('Error responding to invitation:', error);
      showError('Error', 'Failed to respond to invitation');
    } finally {
      setResponding(null);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }
  };

  // Get permission level color
  const getPermissionColor = (level: string) => {
    switch (level) {
      case 'VIEW':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'EDIT':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'DELETE':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Fetch invitations on component mount and when auth status changes
  useEffect(() => {
    fetchInvitations();
  }, [status, session]);

  // Auto-refresh invitations every 30 seconds
  useEffect(() => {
    if (status === 'authenticated') {
      const interval = setInterval(fetchInvitations, 30000);
      return () => clearInterval(interval);
    }
  }, [status]);

  // Don't render if not authenticated or no invitations
  if (status !== 'authenticated' || invitations.length === 0) {
    return <ToastContainer />;
  }

  return (
    <>
      <div className={`mb-6 ${className}`}>
        <Card className="border-l-4 border-l-blue-500 bg-blue-50/50">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Mail className="h-5 w-5 text-blue-600" />
                <CardTitle className="text-lg text-blue-900">
                  Dashboard Invitations
                </CardTitle>
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  {invitations.length}
                </Badge>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-blue-600 hover:text-blue-800"
              >
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </CardHeader>
          
          {isExpanded && (
            <CardContent className="pt-0">
              <div className="space-y-4">
                {invitations.map((invitation) => (
                  <div
                    key={invitation.id}
                    className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <User className="h-4 w-4 text-gray-500" />
                          <span className="font-medium text-gray-900">
                            {invitation.owner_name || invitation.owner_email}
                          </span>
                          <span className="text-gray-500">invited you to access their dashboard</span>
                        </div>
                        
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                          <div className="flex items-center space-x-1">
                            <Shield className="h-3 w-3" />
                            <span>Permission:</span>
                            <Badge 
                              variant="outline" 
                              className={getPermissionColor(invitation.permission_level)}
                            >
                              {invitation.permission_level}
                            </Badge>
                          </div>
                          
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>{formatDate(invitation.created_at)}</span>
                          </div>
                        </div>
                        
                        <div className="text-xs text-gray-500 mb-3">
                          <AlertCircle className="h-3 w-3 inline mr-1" />
                          Expires on {new Date(invitation.expires_at).toLocaleDateString()}
                        </div>
                      </div>
                      
                      <div className="flex space-x-2 ml-4">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => respondToInvitation(invitation.id, 'accept')}
                          disabled={responding === invitation.id}
                          className="bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
                        >
                          <Check className="h-3 w-3 mr-1" />
                          Accept
                        </Button>
                        
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => respondToInvitation(invitation.id, 'reject')}
                          disabled={responding === invitation.id}
                          className="bg-red-50 border-red-200 text-red-700 hover:bg-red-100"
                        >
                          <X className="h-3 w-3 mr-1" />
                          Reject
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {loading && (
                <div className="text-center py-4">
                  <div className="inline-flex items-center space-x-2 text-gray-500">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span>Loading invitations...</span>
                  </div>
                </div>
              )}
            </CardContent>
          )}
        </Card>
      </div>
      
      <ToastContainer />
    </>
  );
};

export default InvitationNotifications;
