import type { APIRoute } from 'astro';
import { getDatabase } from '../../../lib/database';
import { getUserFromRequest, isAuthenticated } from '../../../lib/auth-utils';
import type { MyInvitationsResponse, Invitation } from '../../../types/dashboard';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Get database connection
    const db = getDatabase(locals.runtime.env);

    // Check authentication
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication required'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user information
    const user = getUserFromRequest(request);

    if (!user || !user.email) {
      return new Response(JSON.stringify({
        success: false,
        error: 'User email not found'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get pending invitations for the user's email
    const invitationsResult = await db.prepare(`
      SELECT 
        si.id,
        si.owner_id,
        si.email,
        si.permission_level,
        si.token,
        si.status,
        si.expires_at,
        si.created_at,
        u.name as owner_name,
        u.email as owner_email
      FROM share_invitations si
      LEFT JOIN users u ON si.owner_id = u.id
      WHERE si.email = ? 
        AND si.status = 'pending' 
        AND si.expires_at > datetime('now')
      ORDER BY si.created_at DESC
    `).bind(user.email.toLowerCase()).all();

    // Transform database results to API format
    const invitations: Invitation[] = invitationsResult.results.map((row: any) => ({
      id: row.id,
      owner_id: row.owner_id,
      owner_name: row.owner_name,
      owner_email: row.owner_email,
      email: row.email,
      permission_level: row.permission_level,
      token: row.token,
      status: row.status,
      expires_at: row.expires_at,
      created_at: row.created_at
    }));

    console.log(`Found ${invitations.length} pending invitations for ${user.email}`);

    const response: MyInvitationsResponse = {
      success: true,
      data: invitations
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('Error fetching invitations:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
